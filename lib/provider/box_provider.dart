import 'package:get_storage/get_storage.dart';
import 'package:sbar_pos/hive/helper/boxes.dart';
import 'package:talker/talker.dart';

class BoxProvider {
  final Talker talker;
  String _namespace = '';

  BoxProvider(this.talker);

  Future<void> init(String namepsace) async {
    _namespace = namepsace;
    for (var box in Boxes.values) {
      if (!await _initGsBox(box.name)) {
        talker.error('Failed to initialize GetStorage for box: ${box.name}');
      }
    }
  }

  Future<bool> _initGsBox(String name) {
    final fullName = _getFullName(name);
    return GetStorage.init(fullName);
  }

  GetStorage getGsBox(Boxes box, {bool withNamespace = true}) {
    final fullName = _getFullName(box.name, withNamespace);
    return GetStorage(fullName);
  }

  String _getFullName(String name, [bool withNamespace = true]) {
    Iterable<String> children([bool withNamespace = true]) sync* {
      yield name;
      if (withNamespace && _namespace.isNotEmpty) {
        yield _namespace;
      }
    }

    return children(withNamespace).join('.');
  }
}
