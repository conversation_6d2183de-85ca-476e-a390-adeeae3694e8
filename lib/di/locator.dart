import 'dart:ui';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:get_storage/get_storage.dart';
import 'package:sbar_pos/flavor/flavor.dart';
import 'package:sbar_pos/api/dio_auth_interceptor.dart';
import 'package:sbar_pos/hive/helper/boxes.dart';
import 'package:sbar_pos/model/member/member_repository.dart';
import 'package:sbar_pos/api/all_api.dart';
import 'package:sbar_pos/api/basic_api.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:talker_dio_logger/talker_dio_logger.dart';
import 'package:sbar_pos/provider/box_provider.dart';

final GetIt getIt = GetIt.instance;

Future<void> setupLocator() async {
  final talker = getIt.registerSingleton<Talker>(TalkerFlutter.init());

  // 全域錯誤攔截：將 Flutter framework 與原生平台層錯誤導入 talker
  FlutterError.onError = (FlutterErrorDetails details) {
    // 先交由 talker 記錄詳細錯誤與 stack
    talker.handle(details.exception, details.stack);
    // 保持 Flutter 預設錯誤處理行為（例如開發模式下紅字錯誤畫面）
    FlutterError.presentError(details);
  };

  // 全域錯誤攔截：將原生平台層錯誤導入 talker
  PlatformDispatcher.instance.onError = (Object error, StackTrace stack) {
    talker.handle(error, stack);
    // 回傳 true 表示錯誤已被處理，避免應用直接崩潰
    return true;
  };

  // 註冊 BoxProvider（依賴 Talker）
  if (!getIt.isRegistered<BoxProvider>()) {
    getIt.registerLazySingleton<BoxProvider>(() => BoxProvider(talker));
  }

  // 註冊 MemberRepository（使用專案現有的全域實例）
  if (!getIt.isRegistered<MemberRepository>()) {
    getIt.registerLazySingleton<MemberRepository>(
      () => getAuthMemberRepository,
    );
  }

  // Dio 單例註冊
  if (!getIt.isRegistered<Dio>()) {
    final baseUrl = FlavorConfig.instance.values.baseUrl;
    final options = BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 15),
      receiveTimeout: const Duration(seconds: 20),
      sendTimeout: const Duration(seconds: 20),
      contentType: 'application/json',
    );

    final dio = Dio(options);
    // 掛上授權攔截器與 Talker Dio Logger
    final boxProvider = getIt<BoxProvider>();
    final box = boxProvider.getGsBox(Boxes.settings, withNamespace: false);
    dio.interceptors.addAll([
      DioAuthInterceptor(box),
      TalkerDioLogger(
        talker: talker,
        settings: const TalkerDioLoggerSettings(
          printRequestData: true,
          printRequestHeaders: true,
          printResponseData: true,
          printResponseHeaders: false,
          printErrorData: true,
        ),
      ),
    ]);

    getIt.registerLazySingleton<Dio>(() => dio);
  }

  // 註冊 BasicApi（依賴 Dio）
  if (!getIt.isRegistered<BasicApi>()) {
    getIt.registerLazySingleton<BasicApi>(() => BasicApi(getIt<Dio>()));
  }

  // 註冊 AllApi（依賴 BasicApi）
  if (!getIt.isRegistered<AllApi>()) {
    getIt.registerLazySingleton<AllApi>(() => AllApi(getIt<BasicApi>()));
  }

  // 若未來有其他需要初始化的 async 單例，可在這裡 await allReady
  // await getIt.allReady();
}
